import random
from app.dto.mat_req_dto import (
    CreateMatReq,
    TriggerMatReqOrderResponse,
)


# def mock_mat_req_CheckStockAvailable_response(payload: CreateMatReq):
#     """Mock response for Micron API CheckStockAvailable, No official response from Micron about the response yet ( JUST AN ASSUMPTION )"""
#     data = []
#     list_of_bundle_info = payload.mat_req_details
#     for bundle_info in list_of_bundle_info:
#         data.append(
#             CreateMatReqDetailsWithQuantity(
#                 micron_part_num=bundle_info.micron_part_num,
#                 unit_of_measures="Die",
#                 quantity=random.randint(10000, 25000),  # Randomize quantity
#                 material_type=bundle_info.material_type,
#             )
#         )
#     return CreateMatReqWithQuantity(mat_req_details=data)


def mock_mat_req_TriggerMatReqOrder_response(payload: CreateMatReq):
    """Mock response for Micron API TriggerMatReqOrder, No official response from Micron about the response yet ( JUST AN ASSUMPTION )"""
    return TriggerMatReqOrderResponse(
        mat_req_no=f"{random.randint(0000000000, 9999999999)}",
    )
