from sqlalchemy.ext.asyncio.session import AsyncSession

from app.core.decorators import BadRequestException
from app.dal.bin_dal import Bin, BinCreate, BinDAL, BinFilter
from app.models.entities.bin_model import (
    Location,
    validate_bin_is_active,
    validate_bin_is_found,
    validate_bin_is_reserved,
)
from app.models.schemas.bin_schema import BinDropDownOut
from app.utils.enum import BinStatus


class BinController:

    def __init__(self, session: AsyncSession) -> None:
        self.bin_dal = BinDAL(session=session)
        self.session: AsyncSession = session

    async def create_bin(self, bin_create: BinCreate) -> Bin:
        """Create a new bin"""
        duplicate_bin: Bin = await self.bin_dal.get_bin(bin_filter=BinFilter(code=bin_create.code))

        if duplicate_bin is not None:
            raise BadRequestException(original_exception=f"Bin {bin_create.code} is already created")

        return await self.bin_dal.create_bin(bin=bin_create)

    async def get_bin_drop_down(self) -> BinDropDownOut:
        """Get bin drop down"""
        return BinDropDownOut(
            status=BinStatus.values(),
        )

    async def get_empty_bins(self) -> list[Bin]:
        """Get empty bins"""
        return await self.bin_dal.get_bins(
            bin_filter=BinFilter(is_empty=True, status=BinStatus.ACTIVE, is_include_bundles=True),
        )

    async def update_bin_moving(self, bin_code: str) -> Bin:
        """Update bin moving by reserving it"""
        bin: Bin = validate_bin_is_found(
            bin=await self.bin_dal.get_bin(bin_filter=BinFilter(code=bin_code)),
            error_message=f"Bin {bin_code} not found",
        )

        validate_bin_is_active(bin=bin, error_message=f"Bin {bin_code} is not active")

        bin.move()
        return await self.bin_dal.update_bin(bin=bin)

    # ? if location entity is able to be provided to reduce db io calls (may have a method to unreserve location in location controller)
    async def update_bin_move_complete(self, bin_code: str, location: Location) -> Bin:
        """Update bin move complete to location and unreserve it"""
        bin: Bin = validate_bin_is_found(
            bin=await self.bin_dal.get_bin(bin_filter=BinFilter(code=bin_code)),
            error_message=f"Bin {bin_code} not found",
        )

        validate_bin_is_reserved(bin=bin, error_message=f"Bin {bin_code} is not reserved")

        bin.move_complete(location=location)
        return await self.bin_dal.update_bin(bin=bin)

    async def update_bin_cancel_move(self, bin_code: str) -> Bin:
        """Update bin move complete to location and unreserve it"""
        bin: Bin = validate_bin_is_found(
            bin=await self.bin_dal.get_bin(bin_filter=BinFilter(code=bin_code)),
            error_message=f"Bin {bin_code} not found",
        )

        validate_bin_is_reserved(bin=bin, error_message=f"Bin {bin_code} is not reserved")

        bin.move_cancel()
        return await self.bin_dal.update_bin(bin=bin)
