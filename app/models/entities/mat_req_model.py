from sqlalchemy import Enum
from typing import List, Dict, Any
from sqlmodel import SQLModel, Field, Column, JSON

from app.utils.enum import MatReqStatus
from app.utils.conversion import convert_utc_to_local_iso
from app.models.entities.base_timestamp import BaseTimestamp


class MatReqBase(SQLModel):
    id: int = Field(primary_key=True)
    mat_req_no: str = Field(nullable=True)
    status: str = Field(sa_column=Enum(MatReqStatus), default=MatReqStatus.PENDING)
    details: List[Dict[str, Any]] = Field(sa_column=Column(JSON), default=[])


class MatReq(BaseTimestamp, MatReqBase, table=True):
    __tablename__ = "material_request"

    class Config:
        from_attributes = True
