# Use Python base image
FROM python:3.12.5-slim-bullseye
WORKDIR /app

RUN apt-get update && apt-get install -y \
    openssh-client \
    apt-transport-https \
    curl \
    gnupg \
    git \
    libodbc1 \
    unixodbc-dev

# Add the Microsoft GPG key and repository
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
&& curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
&& apt-get update

# Install the ODBC Driver 17 for SQL Server
RUN ACCEPT_EULA=Y apt-get install -y msodbcsql17

COPY requirements.txt requirements.txt
RUN python -m pip install --upgrade pip
# RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts
RUN pip3 install --default-timeout=100 -r requirements.txt

COPY . .
# Set the startup command
CMD ["python3", "-u", "run.py"]