from pydantic import BaseModel
from pydantic.functional_validators import field_validator, model_validator

from app.core.decorators import BadRequestException
from app.core.response import APIResponse
from app.utils.enum import LocationStatus, LocationType, LocationZone


class LocationCreate(BaseModel):
    zone: LocationZone
    type: LocationType
    x_coordinate: int | None = 0
    y_coordinate: int | None = 0

    class Config:
        from_attributes = True

    @model_validator(mode="before")
    @classmethod
    def validate_coordinate_is_completely_provided(cls, values: dict) -> dict:
        x: int | None = values.get("x_coordinate")
        y: int | None = values.get("y_coordinate")
        if x is None and y is None:
            return values
        if x is None or y is None:
            raise BadRequestException(
                original_exception="Both x and y coordinates must be provided if either one of them is provided"
            )
        return values

    @model_validator(mode="before")
    @classmethod
    def validate_mms_location_have_coordinate(cls, values: dict) -> dict:
        type: LocationType = values.get("type")
        x: int | None = values.get("x_coordinate")
        y: int | None = values.get("y_coordinate")
        if type != LocationType.MMS:
            return values
        if x is None or y is None:
            raise BadRequestException(
                original_exception=f"Both x and y coordinates must be provided for location with type {LocationType.MMS}"
            )
        return values

    @field_validator("zone", mode="before")
    @classmethod
    def validate_zone_is_included_in_enum(cls, value: LocationZone) -> LocationZone:
        if value is None:
            raise BadRequestException(original_exception="Location zone is required")
        if value not in LocationZone.values():
            raise BadRequestException(
                original_exception=f"Invalid location zone {value}. Please provide location zone that is included in following list ({LocationZone.join_values()})"
            )
        return value

    @field_validator("type", mode="before")
    @classmethod
    def validate_type_is_included_in_enum(cls, value: LocationType) -> LocationType:
        if value is None:
            raise BadRequestException(original_exception="Location type is required")
        if value not in LocationType.values():
            raise BadRequestException(
                original_exception=f"Invalid location type {value}. Please provide location type that is included in following list ({LocationType.join_values()})"
            )
        return value


class LocationDropDownOut(BaseModel):
    zone: list[str]
    type: list[str]
    status: list[str]

    class Config:
        from_attributes = True


class LocationReserveUpdate(BaseModel):
    zone: LocationZone
    x_coordinate: int | None = 0
    y_coordinate: int | None = 0

    class Config:
        from_attributes = True


class LocationOut(BaseModel):
    id: int
    zone: LocationZone
    type: LocationType
    x_coordinate: int
    y_coordinate: int
    status: LocationStatus

    class Config:
        from_attributes = True


class LocationOutResponse(APIResponse):
    data: list[LocationOut] = []
