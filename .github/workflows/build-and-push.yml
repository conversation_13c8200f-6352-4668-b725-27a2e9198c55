name: Build and Push to ECR

on:
  push:
    branches:
      - master
  workflow_dispatch:
    inputs:
      tag:
        description: 'Docker image tag'
        required: true
        default: 'latest'
        type: choice
        options:
          - latest
          - stable
          - dev
          - test

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Set image tag
        id: set-tag
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "TAG=${{ github.event.inputs.tag }}" >> $GITHUB_OUTPUT
          else
            echo "TAG=latest" >> $GITHUB_OUTPUT
          fi

      - name: Build and push image to ECR
        id: docker_build
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/micron-be:${{ steps.set-tag.outputs.TAG }}
          platforms: linux/amd64
          cache-from: type=gha
          cache-to: type=gha,mode=max
          provenance: false

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}
