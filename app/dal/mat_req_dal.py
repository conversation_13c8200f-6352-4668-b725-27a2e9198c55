from typing import List
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession

from .base_dal import BaseDAL
from app.dto.mat_req_dto import CreateMatReq
from app.models.entities.mat_req_model import MatReq


class MatReqDAL(BaseDAL):

    def __init__(self, session: AsyncSession):
        super().__init__(session)

    async def create_mat_req(self, mat_req_no: str, mat_req_info: CreateMatReq) -> MatReq:
        details = [bundle.model_dump() for bundle in mat_req_info.mat_req_details]
        mat_req = MatReq(mat_req_no=mat_req_no, details=details)
        self.session.add(mat_req)
        await self.session.flush()
        await self.session.refresh(mat_req)
        return mat_req

    async def get_pending_mat_req(self) -> List[MatReq]:
        result = await self.session.execute(
            select(MatReq).where(MatReq.status == "PENDING").order_by(MatReq.created_at.desc())
        )
        return list(result.scalars().all())
