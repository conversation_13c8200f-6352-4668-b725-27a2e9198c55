from typing import TYPE_CHECKING
from pydantic import PrivateAttr
from pydantic.main import BaseModel
from sqlmodel import Relationship, SQLModel, Field
from sqlalchemy import Enum
from app.core.decorators import BadRequestException
from app.utils.enum import BinStatus
from app.models.entities.base_timestamp import BaseTimestamp

if TYPE_CHECKING:
    from app.models.entities.location_model import Location


class BinBase(SQLModel):
    id: int = Field(primary_key=True)
    code: str
    location_id: int | None = Field(foreign_key="locations.id")
    is_full: bool = Field(default=False)
    status: str = Field(sa_column=Enum(BinStatus), default=BinStatus.ACTIVE)

    class Config:
        from_attributes = True


class Bin(BaseTimestamp, BinBase, table=True):
    __tablename__ = "bins"

    location: "Location" = Relationship(back_populates="bins")
    _bundles: list[str] = PrivateAttr(default_factory=list)  # TODO: add bundles relationship

    def is_empty(self) -> bool:
        return len(self._bundles) == 0 and not self.is_full

    def is_active(self) -> bool:
        return self.status == BinStatus.ACTIVE

    def move(self) -> None:
        self.status = BinStatus.RESERVED

    def move_cancel(self) -> None:
        self.status = BinStatus.ACTIVE

    def move_complete(self, location: "Location") -> None:
        self.status = BinStatus.ACTIVE
        self.location_id = location.id

    class Config:
        from_attributes = True


class BinFilter(BaseModel):
    code: str | None = None
    location_id: int | None = None
    is_full: bool | None = None
    status: BinStatus | None = None
    is_include_bundles: bool = False
    is_empty: bool = False

    class Config:
        from_attributes = True


def validate_bin_is_found(
    bin: Bin | None, error_message: str = "Bin not found", is_internal_exception: bool = False
) -> Bin:
    if bin is not None:
        return bin
    if is_internal_exception:
        raise Exception(error_message)
    raise BadRequestException(original_exception=error_message)


def validate_bin_is_active(
    bin: Bin, error_message: str = "Bin is not active", is_internal_exception: bool = False
) -> Bin:
    if bin.status is BinStatus.ACTIVE:
        return bin
    if is_internal_exception:
        raise Exception(error_message)
    raise BadRequestException(original_exception=error_message)


def validate_bin_is_reserved(
    bin: Bin, error_message: str = "Bin is not reserved", is_internal_exception: bool = False
) -> Bin:
    if bin.status is BinStatus.RESERVED:
        return bin
    if is_internal_exception:
        raise Exception(error_message)
    raise BadRequestException(original_exception=error_message)
