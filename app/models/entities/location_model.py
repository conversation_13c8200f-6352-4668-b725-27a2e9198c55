from typing import TYPE_CHECKING
from pydantic.main import BaseModel
from sqlmodel import Relationship, SQLModel, Field
from sqlalchemy import Enum
from app.core.decorators import BadRequestException
from app.utils.enum import LocationZone, LocationType, LocationStatus
from app.models.entities.base_timestamp import BaseTimestamp

if TYPE_CHECKING:
    from app.models.entities.bin_model import Bin


class LocationBase(SQLModel):
    id: int = Field(primary_key=True)
    zone: str = Field(sa_column=Enum(LocationZone))
    type: str = Field(sa_column=Enum(LocationType))
    x_coordinate: int = Field(default=0)
    y_coordinate: int = Field(default=0)
    status: str = Field(sa_column=Enum(LocationStatus), default=LocationStatus.ACTIVE)

    def reserve(self) -> None:
        self.status = LocationStatus.RESERVED

    def unreserve(self) -> None:
        self.status = LocationStatus.ACTIVE

    class Config:
        from_attributes = True


class Location(BaseTimestamp, LocationBase, table=True):
    __tablename__ = "locations"

    bins: list["Bin"] = Relationship(back_populates="location", sa_relationship_kwargs={"lazy": "selectin"})

    def is_empty(self) -> bool:
        return len(self.bins) == 0

    class Config:
        from_attributes = True


class LocationFilter(BaseModel):
    type: LocationType | None = None
    zone: LocationZone | None = None
    x_coordinate: int | None = None
    y_coordinate: int | None = None
    status: LocationStatus | None = None
    is_include_bins: bool = False
    is_empty: bool = False

    class Config:
        from_attributes = True


def validate_location_is_found(
    location: Location | None, error_message: str = "Location not found", is_internal_exception: bool = False
) -> Location:
    if location is not None:
        return location
    if is_internal_exception:
        raise Exception(error_message)
    raise BadRequestException(original_exception=error_message)


def validate_location_is_active(
    location: Location, error_message: str = "Location is not active", is_internal_exception: bool = False
) -> Location:
    if location.status is LocationStatus.ACTIVE:
        return location
    if is_internal_exception:
        raise Exception(error_message)
    raise BadRequestException(original_exception=error_message)


def validate_location_is_reserved(
    location: Location, error_message: str = "Location is not reserved", is_internal_exception: bool = False
) -> Location:
    if location.status is LocationStatus.RESERVED:
        return location
    if is_internal_exception:
        raise Exception(error_message)
    raise BadRequestException(original_exception=error_message)
