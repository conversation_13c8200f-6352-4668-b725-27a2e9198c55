from datetime import datetime, timezone
from typing import Tuple
from sqlalchemy.engine.result import Result
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, asc
from sqlalchemy.orm import selectinload
from sqlalchemy.sql.selectable import Select

from app.models.entities.location_model import Location, LocationFilter
from app.models.schemas.location_schema import LocationCreate


class LocationDAL:
    def __init__(self, session: AsyncSession) -> None:
        self.session: AsyncSession = session

    async def create_location(self, location: LocationCreate) -> Location:
        """Create a new location"""
        db_location = Location(**location.model_dump())
        self.session.add(instance=db_location)
        await self.session.flush()
        await self.session.refresh(instance=db_location)
        return db_location

    async def get_locations(
        self,
        location_filter: LocationFilter | None = None,
        page: int = 1,
        per_page: int = 10,
        is_paging: bool = True,
    ) -> list[Location]:
        """Get locations with optional filtering"""
        query: Select[Tuple[Location]] = select(Location)

        query = self.__eager_load(query=query, location_filter=location_filter)
        query = self.__filter_location(query=query, location_filter=location_filter)

        if is_paging:
            query = (
                query.offset(offset=(page - 1) * per_page)
                .limit(limit=per_page)
                .order_by(asc(column=Location.created_at))
            )

        result: Result[Tuple[Location]] = await self.session.execute(statement=query)
        return list(result.scalars().all())

    async def get_location(self, location_filter: LocationFilter | None = None) -> Location:
        """Get locations with optional filtering"""
        query: Select[Tuple[Location]] = select(Location)

        query = self.__eager_load(query=query, location_filter=location_filter)
        query = self.__filter_location(query=query, location_filter=location_filter)

        result: Result[Tuple[Location]] = await self.session.execute(statement=query)
        return result.scalars().first()

    async def update_location(self, location: Location) -> Location:
        """Update location"""
        location.updated_at = datetime.now(tz=timezone.utc)
        await self.session.flush()
        await self.session.refresh(instance=location)

        return location

    def __filter_location(
        self,
        query: Select[Tuple[Location]],
        location_filter: LocationFilter | None = None,
    ) -> Select[Tuple[Location]]:
        if location_filter is None:
            return query

        # Apply filters
        filters: list = []

        if location_filter.type is not None:
            filters.append(Location.type == location_filter.type)
        if location_filter.zone is not None:
            filters.append(Location.zone == location_filter.zone)
        if location_filter.status is not None:
            filters.append(Location.status == location_filter.status)
        if location_filter.x_coordinate is not None:
            filters.append(Location.x_coordinate == location_filter.x_coordinate)
        if location_filter.y_coordinate is not None:
            filters.append(Location.y_coordinate == location_filter.y_coordinate)

        if filters:
            query = query.where(and_(*filters))

        if location_filter.is_empty is True:
            query = query.where(Location.is_empty())

        return query

    def __eager_load(
        self,
        query: Select[Tuple[Location]],
        location_filter: LocationFilter | None = None,
    ) -> Select[Tuple[Location]]:
        """Eager load relationships"""
        if location_filter is None:
            return query

        if location_filter.is_empty is True:
            location_filter.is_include_bins = True

        if location_filter.is_include_bins:
            query = query.options(selectinload(Location.bins))

        return query
