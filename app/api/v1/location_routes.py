from math import ceil
from typing import Annotated
from fastapi import APIRouter, Depends, status
from fastapi.param_functions import Query
from sqlalchemy.ext.asyncio.session import AsyncSession

from app.controllers.location_controller import LocationController
from app.core.decorators import async_log_and_return_error
from app.core.response import Pagination, Response
from app.db.session import get_session
from app.models.entities.location_model import Location, LocationFilter
from app.models.schemas.location_schema import LocationCreate, LocationDropDownOut, LocationOut, LocationOutResponse
from app.utils.enum import LocationStatus, LocationType, LocationZone
from app.utils.logger import recv_http_logger

router = APIRouter(prefix="/locations", tags=["locations"])


# Dependency to get controller
async def get_location_controller(session: AsyncSession = Depends(dependency=get_session)) -> LocationController:
    return LocationController(session=session)


@router.get(path="/drop-down", status_code=status.HTTP_200_OK, response_model=LocationDropDownOut)
@async_log_and_return_error(log_func=recv_http_logger)
async def get_location_drop_down(
    controller: LocationController = Depends(dependency=get_location_controller),
) -> Response:
    """Get location drop down"""
    location_drop_down_out: LocationDropDownOut = await controller.get_location_drop_down()
    return Response(code=status.HTTP_200_OK, data=location_drop_down_out)


@router.get(path="/", status_code=status.HTTP_200_OK, response_model=LocationOutResponse)
@async_log_and_return_error(log_func=recv_http_logger)
async def get_locations(
    zone: Annotated[LocationZone, Query(description="The location zone")] | None = None,
    type: Annotated[LocationType, Query(description="The location type")] | None = None,
    status: Annotated[LocationStatus, Query(description="The location status")] | None = None,
    page: Annotated[int, Query(description="The page number", ge=1)] | None = 1,
    per_page: Annotated[int, Query(description="The number of logs per page", ge=1)] | None = 100,
    controller: LocationController = Depends(dependency=get_location_controller),
) -> Response:
    """Get location"""
    location_filter: LocationFilter = LocationFilter(
        zone=zone,
        type=type,
        status=status,
    )

    pagination = Pagination(page=page, per_page=per_page, total_items=0, total_pages=0)

    location_out_list: list[LocationOut] = await controller.get_location(
        location_filter=location_filter,
        page_filter=pagination,
    )

    pagination.total_items = len(location_out_list)
    pagination.total_pages = ceil(pagination.total_items / pagination.per_page)

    return Response(api_response_model=LocationOutResponse(data=location_out_list, pagination=pagination))


@router.post(path="/", status_code=status.HTTP_201_CREATED, response_model=LocationOut)
@async_log_and_return_error(log_func=recv_http_logger)
async def create_location(
    location: LocationCreate, controller: LocationController = Depends(dependency=get_location_controller)
) -> Response:
    """Create a new location"""
    location: Location = await controller.create_location(location=location)
    return Response(code=status.HTTP_201_CREATED, data=LocationOut.model_validate(obj=location))
