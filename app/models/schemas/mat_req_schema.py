from pydantic import BaseModel, Field
from typing import List, Dict, Any

from app.core.response import APIResponse
from app.models.entities.mat_req_model import MatReq
from app.utils.conversion import convert_utc_to_local_iso


class MatReqBase(BaseModel):
    """Class that used to convert ORM object to pydantic model for out of context use"""

    id: int
    mat_req_no: str | None = None
    status: str
    details: List[Dict[str, Any]] = []
    created_at: str
    updated_at: str | None = None
    completed_at: str | None = None


class MatReqOut(MatReqBase):
    """Class that used to convert ORM object to pydantic model for out of context use"""

    class Config:
        from_attributes = True

    @classmethod
    def from_sqlmodel(cls, sqlmodel_obj: "MatReq"):
        if sqlmodel_obj is None:
            return None
        return cls(
            id=sqlmodel_obj.id,
            mat_req_no=sqlmodel_obj.mat_req_no,
            status=sqlmodel_obj.status.value if hasattr(sqlmodel_obj.status, "value") else sqlmodel_obj.status,
            details=sqlmodel_obj.details,
            created_at=convert_utc_to_local_iso(sqlmodel_obj.created_at),
            updated_at=convert_utc_to_local_iso(sqlmodel_obj.updated_at) if sqlmodel_obj.updated_at else None,
            completed_at=convert_utc_to_local_iso(sqlmodel_obj.completed_at) if sqlmodel_obj.completed_at else None,
        )


class CreateMatReqResponse(APIResponse):
    data: MatReqOut


class GetPendingMatReqResponse(APIResponse):
    data: List[MatReqOut] = Field(default=[])
