from sqlmodel import Field, SQLModel
from datetime import datetime, timezone
from sqlalchemy import Enum as SAEnum, text

from app.utils.enum import LogLevel


class Log(SQLModel, table=True):
    __tablename__ = "log"

    id: int = Field(primary_key=True)
    level: str = Field(sa_column=SAEnum(LogLevel), default=LogLevel.I)
    module: str = Field(default=None)
    message: str = Field(default=None)
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        nullable=False,
        sa_column_kwargs={"server_default": text("GETUTCDATE()")},
    )
