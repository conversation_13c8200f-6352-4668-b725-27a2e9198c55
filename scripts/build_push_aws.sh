#!/bin/bash

## Usage macOS
# cd scripts
# bash build_push_aws.sh your_tag      

## Usage Windows
## Open PowerShell
## cd scripts
## .\build_push_aws.sh your_tag

# default is "latest" image tag

echo "============Build on platform $OSTYPE ================"

aws ecr get-login-password --region ap-southeast-1| docker login --username AWS --password-stdin 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com

if [[ "$OSTYPE" == "darwin"* ]]; then 
    # unix
    docker buildx build --platform linux/amd64 -t micron-be ../.
else 
    # window
    docker buildx build -t micron-be ../.
fi


image_tag=${1:-latest}

echo "===========Tag image with $image_tag=========="
docker tag micron-be:latest 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/micron-be:$image_tag &&  

echo "===========Push image to ECR============"
docker push 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/micron-be:$image_tag
read -p "Press Enter to continue..."
