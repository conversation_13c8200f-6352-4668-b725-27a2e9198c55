from typing import List
from pydantic import BaseModel, Field


class CreateMatReqDetails(BaseModel):
    # {"micronPN": "310-L300145A384E","unitOfMeasures": "Die","quantity": 25000,"MaterialType": "SBT"}
    micron_part_num: str = Field(..., description="Micron part number", example="310-L300145A384E")
    unit_of_measures: str = Field(..., description="Unit of measures", example="Die")
    quantity: int = Field(..., description="Quantity", example=25000)
    material_type: str = Field(..., description="Material type", example="SBT")


class CreateMatReq(BaseModel):
    # [{"micronPN": "310-L300145A384E","unitOfMeasures": "Die","quantity": 25000,"MaterialType": "SBT"}, ...]
    mat_req_details: List[CreateMatReqDetails] = Field(
        ..., description="Material Request details for this material request"
    )


class TriggerMatReqOrderResponse(BaseModel):
    mat_req_no: str = Field(..., description="Material request number")
