# Environment variables
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Git
.git
.gitignore

# Logs
*.log
logs/

# Test coverage
.coverage
htmlcov/
.pytest_cache/
.tox/

# Docker
Dockerfile
docker-compose*.yml

# Documentation
# docs/
*.md

# Local development
*.sqlite3
*.db