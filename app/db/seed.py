from sqlalchemy import Any, <PERSON><PERSON>, insert
from sqlalchemy.engine.cursor import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio.engine import AsyncConnection
from sqlmodel import select
from app.models.entities.location_model import Location, LocationZone, LocationType, LocationStatus
from app.utils.logger import health_logger


async def seed_locations(conn: AsyncConnection) -> None:
    result: CursorResult[Tuple[Location]] = await conn.execute(statement=select(Location))
    if result.first():
        health_logger.info_to_console_only(msg="ℹ️  Locations already seeded.")
        return

    values: list[dict[str, Any]] = [
        {
            "zone": LocationZone.GP_PORT,
            "type": LocationType.MMS,
            "x_coordinate": 0,
            "y_coordinate": 0,
            "status": LocationStatus.ACTIVE,
        },
        {
            "zone": LocationZone.DEFAULT_PORT,
            "type": LocationType.MMS,
            "x_coordinate": 0,
            "y_coordinate": 0,
            "status": LocationStatus.ACTIVE,
        },
        {
            "zone": LocationZone.SORTATION_PORT,
            "type": LocationType.MMS,
            "x_coordinate": 0,
            "y_coordinate": 0,
            "status": LocationStatus.ACTIVE,
        },
        {
            "zone": LocationZone.REJECT_PORT,
            "type": LocationType.MMS,
            "x_coordinate": 0,
            "y_coordinate": 0,
            "status": LocationStatus.ACTIVE,
        },
    ]

    await conn.execute(statement=insert(table=Location), parameters=values)
    health_logger.info_to_console_only(msg=f"✅ Seeded {len(values)} locations.")
