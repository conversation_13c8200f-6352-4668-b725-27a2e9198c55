from fastapi import APIRouter

from app.db.session import SessionDep
from app.dto.mat_req_dto import CreateMatReq
from app.utils.logger import recv_http_logger
from app.core.decorators import async_log_and_return_error
from app.controllers.mat_req_controller import MatReqController
from app.core.response import Response
from app.models.schemas.mat_req_schema import CreateMatReqResponse, GetPendingMatReqResponse

router = APIRouter(prefix="/mat_req", tags=["mat_req"])


@router.get("/pending-list", response_model=GetPendingMatReqResponse)
@async_log_and_return_error(recv_http_logger)
async def get_pending_mat_req(session: SessionDep):
    mat_req_controller = MatReqController(session)
    mat_reqs = await mat_req_controller.get_pending_mat_req()
    return Response(api_response_model=GetPendingMatReqResponse(data=mat_reqs))


@router.post("/", response_model=CreateMatReqResponse)
@async_log_and_return_error(recv_http_logger)
async def create_mat_req(
    args: CreateMatReq,
    session: SessionDep,
):
    """
    For FE to create a material request \n
    Will call Micron API for verification, if success only create Mat Req on our system
    """
    from app.mock.mock_mat_req import (
        mock_mat_req_TriggerMatReqOrder_response,
    )

    # TODO call micron api TriggerMatReqOrder to start a mat req
    # Order	TriggerMatReqOrder	{"micronPN": "310-L300145A384E","unitOfMeasures": "Die","quantity": 25000,"MaterialType": "SBT"}
    # Assuming the api will response with MatReqNo and reject if not enough quantity

    mock_response = mock_mat_req_TriggerMatReqOrder_response(args)

    mat_req_controller = MatReqController(session)
    mat_req = await mat_req_controller.create_mat_req(mock_response.mat_req_no, args)

    return Response(api_response_model=CreateMatReqResponse(data=mat_req))
