from sqlalchemy import Any, text
from sqlalchemy.engine.cursor import <PERSON>ursor<PERSON><PERSON>ult
from sqlalchemy.ext.asyncio.engine import AsyncConnection, AsyncEngine
from sqlmodel import SQLModel
from sqlalchemy.ext.asyncio import create_async_engine

from app.core.config import CONFIG
from app.db.seed import seed_locations
from app.utils.logger import health_logger

DATABASE_NAME = "micron-be"


async def init_db():
    from app.db.session import engine
    from app.models.entities import (
        http_retry_model,
        log_model,
        job_model,
        order_model,
        setting_model,
        location_model,
        bin_model,
        mat_req_model,
    )

    master_engine: AsyncEngine = create_async_engine(url=CONFIG.DATABASE_URL_MASTER)

    async with master_engine.connect() as conn:
        conn: AsyncConnection = await conn.execution_options(isolation_level="AUTOCOMMIT")
        result: CursorResult[Any] = await conn.execute(
            statement=text("SELECT 1 FROM sys.databases WHERE name = :dbname"), parameters={"dbname": DATABASE_NAME}
        )
        if result.scalar_one_or_none() is None:
            await conn.execute(statement=text(text=f"CREATE DATABASE [{DATABASE_NAME}]"))
            health_logger.info_to_console_only(msg=f"✅ Database {DATABASE_NAME} created.")
        else:
            health_logger.info_to_console_only(msg=f"✅ Database {DATABASE_NAME} already exists.")
    await master_engine.dispose()

    async with engine.begin() as conn:
        await conn.run_sync(fn=SQLModel.metadata.create_all)
        health_logger.info_to_console_only(msg="✅ Tables created.")
        await seed_locations(conn=conn)
