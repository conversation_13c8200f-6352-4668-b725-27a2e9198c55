# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/*
!.vscode/settings.json
!.vscode/launch.json
*.swp
*.swo

# Environment variables
.env

# Logs
logs/
*.log
npm-debug.log*

# Distribution / packaging
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# pytest
.pytest_cache/

# mypy
.mypy_cache/
.cursorrules

.DS_Store