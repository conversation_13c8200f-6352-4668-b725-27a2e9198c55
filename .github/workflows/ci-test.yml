name: Python Application Test

on:
  push:
    branches: [ "release", "test" ]
  pull_request:
    branches-ignore: [ "main", "master" ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mssql:
        image: mcr.microsoft.com/mssql/server:2019-latest
        env:
          ACCEPT_EULA: "Y"
          SA_PASSWORD: "yourStrong(!)Password"
          MSSQL_PID: "Developer"
        ports:
          - "1433:1433"

    steps:
      # === SETUP PHASE ===
      - uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Wait for SQL Server
        run: |
          echo "Waiting 5 seconds for SQL Server to start..."
          sleep 5

      - name: Create .env.test
        run: |
          echo "FAST_ENV=TEST" >> .env.test
          echo "SECRET_KEY=test_secret_key_for_unittest_only" >> .env.test
          echo "FAST_APP_HOST=127.0.0.1" >> .env.test
          echo "FAST_APP_PORT=5000" >> .env.test
          echo "FAST_TCP_IP_HOST=127.0.0.1" >> .env.test
          echo "FAST_TCP_IP_PORT=2525" >> .env.test
          echo "DB_HOST=localhost" >> .env.test
          echo "DB_PORT=1433" >> .env.test
          echo "DB_USER=sa" >> .env.test
          echo 'DB_PASS=yourStrong(!)Password' >> .env.test
          echo "DB_NAME=micron-be-test" >> .env.test
          echo "DB_DRIVER={ODBC Driver 17 for SQL Server}" >> .env.test
          echo "TIME_ZONE=Asia/Kuala_Lumpur" >> .env.test
          echo "STORAGE_IDENTIFIER=0" >> .env.test
          echo "MOCK_TXID=true" >> .env.test
          echo "STORAGE_RR_HARD_RULE=false" >> .env.test

      # === DEPENDENCIES PHASE ===
      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y curl unixodbc-dev
          
          # Add Microsoft repository for ODBC Driver 17 (using Ubuntu 20.04 repo which has Driver 17)
          wget -qO- https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
          echo "deb [arch=amd64] https://packages.microsoft.com/ubuntu/20.04/prod focal main" | sudo tee /etc/apt/sources.list.d/mssql-release.list
          sudo apt-get update
          
          # Install ODBC Driver 17
          sudo ACCEPT_EULA=Y apt-get install -y msodbcsql17 mssql-tools

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      # === TEST PHASE ===
      - name: Verify .env.test exists
        run: |
          echo "Verifying .env.test file exists..."
          ls -la .env.test
          echo "Contents of .env.test:"
          cat .env.test

      - name: Run tests
        run: |
          python -m pytest -v
