from datetime import datetime, timezone
from sqlmodel import SQLModel, Field
from sqlalchemy import text


class BaseTimestamp(SQLModel):
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        nullable=False,
        sa_column_kwargs={"server_default": text("GETUTCDATE()")},
    )

    updated_at: datetime | None = Field(
        default=None, nullable=True, sa_column_kwargs={"onupdate": text("GETUTCDATE()")}
    )
    completed_at: datetime | None = Field(default=None, nullable=True)
