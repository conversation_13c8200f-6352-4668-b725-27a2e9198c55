from typing import List
from sqlalchemy.ext.asyncio import AsyncSession

from .base_controller import BaseController
from app.dal.mat_req_dal import MatReqDAL
from app.dto.mat_req_dto import CreateMatReq
from app.models.schemas.mat_req_schema import MatReqOut


class MatReqController(BaseController):

    def __init__(self, session: AsyncSession):
        super().__init__(session)
        self.dal = MatReqDAL(session)

    async def create_mat_req(self, mat_req_no: str, mat_req_info: CreateMatReq) -> MatReqOut:
        """Create a material request"""
        return MatReqOut.from_sqlmodel(await self.dal.create_mat_req(mat_req_no, mat_req_info))

    async def get_pending_mat_req(self) -> List[MatReqOut]:
        """Get all pending material request"""
        return [MatReqOut.from_sqlmodel(mat_req) for mat_req in await self.dal.get_pending_mat_req()]
