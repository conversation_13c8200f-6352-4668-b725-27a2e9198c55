from datetime import datetime, timezone
from typing import Tuple
from sqlalchemy.engine.result import Result
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, asc
from sqlalchemy.orm import selectinload
from sqlalchemy.sql.selectable import Select

from app.models.entities.bin_model import Bin, BinFilter
from app.models.schemas.bin_schema import BinCreate


class BinDAL:
    def __init__(self, session: AsyncSession) -> None:
        self.session: AsyncSession = session

    async def create_bin(self, bin: BinCreate) -> Bin:
        """Create a new bin"""
        db_bin = Bin(**bin.model_dump())
        self.session.add(instance=db_bin)
        await self.session.flush()
        await self.session.refresh(instance=db_bin)
        return db_bin

    async def get_bins(
        self,
        bin_filter: BinFilter | None = None,
        page: int = 1,
        per_page: int = 10,
        is_paging: bool = True,
    ) -> list[Bin]:
        """Get bins with optional filtering"""
        query: Select[Tuple[Bin]] = select(Bin)

        query = self.__eager_load(query=query, bin_filter=bin_filter)
        query = self.__filter_bin(query=query, bin_filter=bin_filter)

        if is_paging:
            query = (
                query.offset(offset=(page - 1) * per_page).limit(limit=per_page).order_by(asc(column=Bin.created_at))
            )

        result: Result[Tuple[Bin]] = await self.session.execute(statement=query)
        return list(result.scalars().all())

    async def get_bin(self, bin_filter: BinFilter | None = None) -> Bin | None:
        """Get bin with optional filtering"""
        query: Select[Tuple[Bin]] = select(Bin)

        query = self.__eager_load(query=query, bin_filter=bin_filter)
        query = self.__filter_bin(query=query, bin_filter=bin_filter)

        result: Result[Tuple[Bin]] = await self.session.execute(statement=query)
        return result.scalars().one_or_none()

    async def update_bin(self, bin: Bin) -> Bin:
        """Update bin"""
        bin.updated_at = datetime.now(tz=timezone.utc)
        await self.session.flush()
        await self.session.refresh(instance=bin)

        return bin

    def __filter_bin(
        self,
        query: Select[Tuple[Bin]],
        bin_filter: BinFilter | None = None,
    ) -> Select[Tuple[Bin]]:
        """Filter bin"""
        if bin_filter is None:
            return query

        # Apply filters
        filters: list = []

        if bin_filter.status is not None:
            filters.append(Bin.status == bin_filter.status)
        if bin_filter.location_id is not None:
            filters.append(Bin.location_id == bin_filter.location_id)
        if bin_filter.is_full is not None:
            filters.append(Bin.is_full == bin_filter.is_full)
        if bin_filter.code is not None:
            filters.append(Bin.code == bin_filter.code)

        if filters:
            query = query.where(and_(*filters))

        if bin_filter.is_empty is True:
            query = query.where(Bin.is_empty())

        return query

    def __eager_load(
        self,
        query: Select[Tuple[Bin]],
        bin_filter: BinFilter | None = None,
    ) -> Select[Tuple[Bin]]:
        """Eager load relationships"""
        if bin_filter is None:
            return query

        if bin_filter.is_empty is True:
            bin_filter.is_include_bundles = True

        if bin_filter.is_include_bundles:
            query = query.options(selectinload(Bin._bundles))

        return query
