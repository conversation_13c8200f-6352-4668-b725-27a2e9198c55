from sqlalchemy.ext.asyncio.session import AsyncSession

from app.core.decorators import BadRequestException
from app.core.response import Pagination
from app.dal.location_dal import LocationDAL
from app.models.entities.location_model import (
    Location,
    LocationFilter,
    validate_location_is_found,
    validate_location_is_active,
    validate_location_is_reserved,
)
from app.models.schemas.location_schema import LocationCreate, LocationDropDownOut, LocationOut, LocationReserveUpdate
from app.utils.enum import LocationStatus, LocationType, LocationZone


class LocationController:

    def __init__(self, session: AsyncSession) -> None:
        self.location_dal = LocationDAL(session=session)
        self.session: AsyncSession = session

    async def create_location(self, location: LocationCreate) -> Location:
        """Create a new location"""
        duplicate_location: Location = await self.location_dal.get_location(
            location_filter=LocationFilter(
                type=location.type,
                zone=location.zone,
                x_coordinate=location.x_coordinate,
                y_coordinate=location.y_coordinate,
            )
        )

        if duplicate_location is not None:
            raise BadRequestException(
                original_exception=f"Location {location.zone} with type {location.type} {f"on coordinate ({location.x_coordinate},{location.y_coordinate}) " if location.x_coordinate is not None else ""}is already created"
            )

        return await self.location_dal.create_location(location=location)

    async def get_location_drop_down(self) -> LocationDropDownOut:
        """Get location drop down"""
        return LocationDropDownOut(
            zone=LocationZone.values(),
            type=LocationType.values(),
            status=LocationStatus.values(),
        )

    async def get_location(self, location_filter: LocationFilter, page_filter: Pagination) -> list[LocationOut]:
        """Get location"""
        location_list: list[Location] = await self.location_dal.get_locations(
            location_filter=location_filter, page=page_filter.page, per_page=page_filter.per_page
        )

        return [LocationOut.model_validate(obj=location) for location in location_list]

    async def get_empty_locations(self) -> list[Location]:
        """Get empty locations"""
        return await self.location_dal.get_locations(
            location_filter=LocationFilter(is_empty=True, status=LocationStatus.ACTIVE, is_include_bins=True),
        )

    async def update_location_reserve(self, location_reserve_update: LocationReserveUpdate) -> Location:
        """Update location reserve on bin moving in or out"""
        location: Location = validate_location_is_found(
            location=await self.location_dal.get_location(
                location_filter=LocationFilter(
                    zone=location_reserve_update.zone,
                    x_coordinate=location_reserve_update.x_coordinate,
                    y_coordinate=location_reserve_update.y_coordinate,
                )
            ),
            error_message=f"Location {location_reserve_update} not found",
        )

        validate_location_is_active(
            location=location, error_message=f"Location {location_reserve_update} is not active"
        )

        location.reserve()
        return await self.location_dal.update_location(location=location)

    async def update_location_unreserve(self, location_reserve_update: LocationReserveUpdate) -> Location:
        """Update location reserve on bin moving in or out"""
        location: Location = validate_location_is_found(
            location=await self.location_dal.get_location(
                location_filter=LocationFilter(
                    zone=location_reserve_update.zone,
                    x_coordinate=location_reserve_update.x_coordinate,
                    y_coordinate=location_reserve_update.y_coordinate,
                )
            ),
            error_message=f"Location {location_reserve_update} not found",
        )

        validate_location_is_reserved(
            location=location, error_message=f"Location {location_reserve_update} is not reserved"
        )

        location.unreserve()
        return await self.location_dal.update_location(location=location)
